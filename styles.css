/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Header Section */
.article-header {
    margin-bottom: 30px;
}

.article-title {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.2;
}

.article-image {
    text-align: center;
    margin-bottom: 20px;
}

.article-image img {
    width: 100%;
    max-width: 800px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Author Info Section */
.author-info {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 30px;
    border-left: 4px solid #3498db;
}

.author-avatar {
    margin-right: 20px;
}

.author-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    
}

.author-details {
    flex: 1;
}

.author-name {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.3rem;
}

.author-bio {
    color: #666;
    margin-bottom: 10px;
    font-size: 0.95rem;
}

.author-meta {
    display: flex;
    gap: 20px;
    font-size: 0.9rem;
    color: #888;
}

/* Article Content */
.article-content {
    margin-bottom: 40px;
    line-height: 1.8;
}

.article-content h2 {
    color: #2c3e50;
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 1.5rem;
    padding-bottom: 5px;
}

.article-content p {
    margin-bottom: 15px;
    text-align: justify;
}


.content-section {
    display: flex;
    align-items: flex-start;
    margin-bottom: 40px;
    gap: 25px;
}

.content-section:nth-child(even) {
    flex-direction: row-reverse;
}

.section-image {
    flex: 0 0 300px;
}

.section-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
 
}



.section-text {
    flex: 1;
}

.section-text h2 {
    margin-top: 0;
}

/* Comments Section */
.comments-section {
    border-top: 2px solid #e9ecef;
    padding-top: 30px;
}

.comments-section h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.8rem;
}

/* Comment Form */
.comment-form {
    background-color: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.comment-form h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.submit-btn {
    background-color: #3498db;
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.submit-btn:hover {
    background-color: #2980b9;
}





/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    .article-title {
        font-size: 2rem;
    }
    
    .author-info {
        flex-direction: column;
        text-align: center;
    }
    
    .author-avatar {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .author-meta {
        justify-content: center;
    }
    
    .comment {
        flex-direction: column;
    }
    
    .comment-avatar {
        margin-right: 0;
        margin-bottom: 10px;
        text-align: center;
    }
    
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    /* Content sections responsive */
    .content-section {
        flex-direction: column !important;
        gap: 15px;
    }

    .content-section:nth-child(even) {
        flex-direction: column !important;
    }

    .section-image {
        flex: none;
        text-align: center;
    }

    .section-image img {
        max-width: 100%;
        height: auto;
    }
}
